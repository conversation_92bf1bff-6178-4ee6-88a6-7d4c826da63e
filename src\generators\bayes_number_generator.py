"""
贝叶斯号码生成器 - 基于位置级贝叶斯分析直接生成号码
"""

import numpy as np
from typing import List, Tuple, Dict, Any
from collections import Counter, defaultdict
import random
from src.generators.base_generator import NumberGenerator
from src.models.bayes.bayes_selector import BayesSelector


class BayesNumberGenerator(NumberGenerator):
    """基于位置级贝叶斯分析的号码生成器"""
    
    def __init__(self, config: Dict[str, Any] = None):
        super().__init__()
        self.position_analyzers = {}  # 存储每个位置的分析器
        self.historical_data = []
        self.kill_numbers = {'red': [], 'blue': []}
        self.ratio_constraints = {}
        
    def initialize(self, historical_data: List[Tuple[List[int], List[int]]], 
                  kill_numbers: Dict[str, List[int]] = None,
                  ratio_constraints: Dict[str, Any] = None):
        """
        初始化生成器
        
        Args:
            historical_data: 历史开奖数据
            kill_numbers: 杀号数据
            ratio_constraints: 比值约束
        """
        self.historical_data = historical_data
        self.kill_numbers = kill_numbers or {'red': [], 'blue': []}
        self.ratio_constraints = ratio_constraints or {}
        
        # 初始化位置分析器
        self._initialize_position_analyzers()

    def _initialize_position_analyzers(self):
        """初始化各位置的贝叶斯分析器"""
        # 红球5个位置
        for pos in range(5):
            self.position_analyzers[f'red_{pos}'] = self._create_position_analyzer(pos, 'red')
            
        # 蓝球2个位置  
        for pos in range(2):
            self.position_analyzers[f'blue_{pos}'] = self._create_position_analyzer(pos, 'blue')
            
    def _create_position_analyzer(self, position: int, ball_type: str) -> Dict:
        """创建单个位置的分析器"""
        # 提取该位置的历史数据
        position_history = []
        for red_balls, blue_balls in self.historical_data:
            balls = red_balls if ball_type == 'red' else blue_balls
            sorted_balls = sorted(balls)
            if position < len(sorted_balls):
                position_history.append(sorted_balls[position])
                
        if not position_history:
            return {'frequency': {}, 'transitions': {}, 'trends': {}}
            
        # 分析频率分布
        frequency = Counter(position_history)
        
        # 分析转移概率
        transitions = self._calculate_transitions(position_history)
        
        # 分析趋势
        trends = self._calculate_trends(position_history[-20:])  # 最近20期
        
        return {
            'frequency': frequency,
            'transitions': transitions,
            'trends': trends,
            'history': position_history
        }
        
    def _calculate_transitions(self, history: List[int]) -> Dict:
        """计算转移概率"""
        transitions = defaultdict(lambda: defaultdict(int))
        
        for i in range(len(history) - 1):
            current = history[i]
            next_val = history[i + 1]
            transitions[current][next_val] += 1
            
        # 转换为概率
        transition_probs = {}
        for current, next_counts in transitions.items():
            total = sum(next_counts.values())
            if total > 0:
                transition_probs[current] = {
                    next_val: count / total 
                    for next_val, count in next_counts.items()
                }
                
        return transition_probs
        
    def _calculate_trends(self, recent_history: List[int]) -> Dict:
        """计算趋势分析"""
        if len(recent_history) < 3:
            return {}
            
        trends = {}
        
        # 计算平均值趋势
        mid_point = len(recent_history) // 2
        early_avg = np.mean(recent_history[:mid_point])
        recent_avg = np.mean(recent_history[mid_point:])
        
        trends['direction'] = 'up' if recent_avg > early_avg else 'down'
        trends['strength'] = abs(recent_avg - early_avg)
        
        # 计算变化幅度
        changes = [abs(recent_history[i] - recent_history[i-1]) 
                  for i in range(1, len(recent_history))]
        trends['volatility'] = np.mean(changes) if changes else 0
        
        return trends
        
    def generate_combinations(self, count: int = 10) -> List[Tuple[List[int], List[int]]]:
        """
        生成指定数量的号码组合
        
        Args:
            count: 生成组合数量
            
        Returns:
            List[Tuple[List[int], List[int]]]: 生成的组合列表
        """
        combinations = []
        max_attempts = count * 10  # 最多尝试次数
        attempts = 0

        while len(combinations) < count and attempts < max_attempts:
            attempts += 1
            
            try:
                # 生成单个组合
                red_balls, blue_balls = self._generate_single_combination()
                
                # 检查约束条件
                if self._check_constraints(red_balls, blue_balls):
                    combinations.append((red_balls, blue_balls))

            except Exception as e:
                print(f"    ⚠️ 生成第{attempts}次尝试失败: {e}")
                continue
                
        return combinations
        
    def _generate_single_combination(self) -> Tuple[List[int], List[int]]:
        """生成单个号码组合"""
        red_balls = []
        blue_balls = []
        
        # 生成红球 (5个位置)
        for pos in range(5):
            number = self._generate_position_number(pos, 'red', red_balls)
            red_balls.append(number)
            
        # 生成蓝球 (2个位置)
        for pos in range(2):
            number = self._generate_position_number(pos, 'blue', blue_balls)
            blue_balls.append(number)
            
        # 排序
        red_balls.sort()
        blue_balls.sort()
        
        return red_balls, blue_balls
        
    def _generate_position_number(self, position: int, ball_type: str, 
                                existing_numbers: List[int]) -> int:
        """为指定位置生成号码"""
        analyzer_key = f'{ball_type}_{position}'
        analyzer = self.position_analyzers.get(analyzer_key, {})
        
        # 确定候选号码范围
        if ball_type == 'red':
            candidates = list(range(1, 36))
            kill_numbers = self.kill_numbers.get('red', [])
        else:
            candidates = list(range(1, 13))
            kill_numbers = self.kill_numbers.get('blue', [])
            
        # 过滤杀号和已选号码
        valid_candidates = [
            num for num in candidates 
            if num not in kill_numbers and num not in existing_numbers
        ]
        
        if not valid_candidates:
            # 如果没有有效候选，随机选择一个不重复的号码
            available = [num for num in candidates if num not in existing_numbers]
            return random.choice(available) if available else candidates[0]
            
        # 收集贝叶斯证据
        evidence = self._collect_position_evidence(analyzer, valid_candidates)
        
        # 计算后验概率
        posterior_probs = self._calculate_position_posterior(evidence, valid_candidates)
        
        # 基于概率选择号码
        return self._select_number_by_probability(valid_candidates, posterior_probs)
        
    def _collect_position_evidence(self, analyzer: Dict, candidates: List[int]) -> List[Dict]:
        """收集位置的贝叶斯证据"""
        evidence = []
        
        # 频率证据
        frequency_dist = analyzer.get('frequency', {})
        total_freq = sum(frequency_dist.values()) if frequency_dist else 1
        freq_evidence = {}
        for num in candidates:
            freq_evidence[num] = frequency_dist.get(num, 0.1) / total_freq
        evidence.append(freq_evidence)
        
        # 转移概率证据
        transitions = analyzer.get('transitions', {})
        history = analyzer.get('history', [])
        if history:
            last_number = history[-1]
            trans_probs = transitions.get(last_number, {})
            trans_evidence = {}
            for num in candidates:
                trans_evidence[num] = trans_probs.get(num, 1.0 / len(candidates))
            evidence.append(trans_evidence)
            
        # 趋势证据
        trends = analyzer.get('trends', {})
        trend_evidence = {}
        for num in candidates:
            # 基于趋势方向调整概率
            if trends.get('direction') == 'up':
                trend_evidence[num] = (num / max(candidates)) ** 0.5
            elif trends.get('direction') == 'down':
                trend_evidence[num] = ((max(candidates) - num) / max(candidates)) ** 0.5
            else:
                trend_evidence[num] = 0.5
        evidence.append(trend_evidence)
        
        return evidence
        
    def _calculate_position_posterior(self, evidence: List[Dict], 
                                    candidates: List[int]) -> Dict[int, float]:
        """计算位置的后验概率"""
        # 初始化贝叶斯选择器
        bayes_selector = BayesSelector('position')
        
        # 设置先验概率 (均匀分布)
        prior_probs = {str(num): 1.0 / len(candidates) for num in candidates}
        bayes_selector.set_prior_probabilities(prior_probs)
        
        # 转换证据格式
        likelihood_sources = []
        for evidence_dict in evidence:
            likelihood_source = {str(num): prob for num, prob in evidence_dict.items()}
            likelihood_sources.append(likelihood_source)
            
        # 计算后验概率
        weights = [0.4, 0.3, 0.3]  # 频率、转移、趋势权重
        posterior_probs = bayes_selector.calculate_posterior_probabilities(
            likelihood_sources, weights
        )
        
        # 转换回整数键，安全处理字符串键
        result = {}
        for k, v in posterior_probs.items():
            try:
                num_key = int(k)
                if num_key in candidates:
                    result[num_key] = v
            except (ValueError, TypeError):
                # 跳过无法转换为整数的键
                continue

        return result
        
    def _select_number_by_probability(self, candidates: List[int], 
                                    probabilities: Dict[int, float]) -> int:
        """基于概率选择号码"""
        if not probabilities:
            return random.choice(candidates)
            
        # 归一化概率
        total_prob = sum(probabilities.values())
        if total_prob <= 0:
            return random.choice(candidates)
            
        normalized_probs = [probabilities.get(num, 0) / total_prob for num in candidates]
        
        # 基于概率进行加权随机选择
        return np.random.choice(candidates, p=normalized_probs)
        
    def _check_constraints(self, red_balls: List[int], blue_balls: List[int]) -> bool:
        """检查约束条件"""
        # 检查杀号约束
        if any(ball in self.kill_numbers.get('red', []) for ball in red_balls):
            return False
        if any(ball in self.kill_numbers.get('blue', []) for ball in blue_balls):
            return False
            
        # 检查比值约束
        if not self._check_ratio_constraints(red_balls, blue_balls):
            return False
            
        return True
        
    def _check_ratio_constraints(self, red_balls: List[int], blue_balls: List[int]) -> bool:
        """检查比值约束"""
        if not self.ratio_constraints:
            return True
            
        try:
            # 检查红球奇偶比
            red_odd_count = sum(1 for ball in red_balls if ball % 2 == 1)
            red_even_count = 5 - red_odd_count
            
            # 检查红球大小比 (1-17小号, 18-35大号)
            red_small_count = sum(1 for ball in red_balls if ball <= 17)
            red_large_count = 5 - red_small_count
            
            # 检查蓝球大小比 (1-6小号, 7-12大号)
            blue_small_count = sum(1 for ball in blue_balls if ball <= 6)
            blue_large_count = 2 - blue_small_count
            
            # 这里可以添加更严格的比值检查逻辑
            # 目前允许所有合理的比值组合
            
            return True

        except Exception:
            return True
